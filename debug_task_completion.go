package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

func main() {
	// Initialize logger
	logger, _ := zap.NewDevelopment()
	global.GVA_LOG = logger

	// Initialize database connection (you'll need to set this up)
	// global.GVA_DB = ... (your database connection)

	// Test user ID from the log
	userID, _ := uuid.Parse("019629ba-7877-7a9c-bac0-0ee50d7e8bd5")
	
	// Initialize services
	service := activity_cashback.NewActivityCashbackService()
	
	ctx := context.Background()
	
	// Get user tier info before
	tierInfo, err := service.GetUserTierInfo(ctx, userID)
	if err != nil {
		log.Printf("Error getting user tier info: %v", err)
		return
	}
	
	fmt.Printf("User tier info before:\n")
	fmt.Printf("  User ID: %s\n", tierInfo.UserID)
	fmt.Printf("  Total Points: %d\n", tierInfo.TotalPoints)
	fmt.Printf("  Current Tier: %d\n", tierInfo.CurrentTier)
	fmt.Printf("  Last Activity: %v\n", tierInfo.LastActivityDate)
	
	// Get tasks with MEME_TRADE_DAILY identifier
	tasks, err := service.GetTasksByCategory(ctx, model.CategoryTrading)
	if err != nil {
		log.Printf("Error getting tasks: %v", err)
		return
	}
	
	var memeTradeTask *model.ActivityTask
	for _, task := range tasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDMemeTradeDaily {
			memeTradeTask = &task
			break
		}
	}
	
	if memeTradeTask == nil {
		log.Printf("MEME trade task not found")
		return
	}
	
	fmt.Printf("\nMEME Trade Task:\n")
	fmt.Printf("  ID: %s\n", memeTradeTask.ID)
	fmt.Printf("  Name: %s\n", memeTradeTask.Name)
	fmt.Printf("  Points: %d\n", memeTradeTask.Points)
	fmt.Printf("  Frequency: %s\n", memeTradeTask.Frequency)
	fmt.Printf("  Category: %s\n", memeTradeTask.Category.Name)
	
	// Get task progress
	progress, err := service.GetTaskProgress(ctx, userID, memeTradeTask.ID)
	if err != nil {
		log.Printf("Error getting task progress: %v", err)
		// Try to initialize
		progress, err = service.InitializeTaskProgress(ctx, userID, memeTradeTask.ID)
		if err != nil {
			log.Printf("Error initializing task progress: %v", err)
			return
		}
	}
	
	fmt.Printf("\nTask Progress:\n")
	fmt.Printf("  Status: %s\n", progress.Status)
	fmt.Printf("  Completion Count: %d\n", progress.CompletionCount)
	fmt.Printf("  Last Completed At: %v\n", progress.LastCompletedAt)
	fmt.Printf("  Points Earned: %d\n", progress.PointsEarned)
	
	// Try to complete the task
	tradeData := map[string]interface{}{
		"volume":     0.00519948,
		"trade_type": "MEME",
		"order_id":   "test-order-id",
		"user_id":    userID.String(),
	}
	
	fmt.Printf("\nAttempting to complete task with data: %+v\n", tradeData)
	
	// Use task registry to process the task
	registry := activity_cashback.NewTaskRegistry(service)
	err = registry.ProcessTaskByIdentifier(ctx, userID, model.TaskIDMemeTradeDaily, model.CategoryTrading, tradeData)
	if err != nil {
		log.Printf("Error processing task: %v", err)
	} else {
		fmt.Printf("Task processed successfully\n")
	}
	
	// Get user tier info after
	tierInfoAfter, err := service.GetUserTierInfo(ctx, userID)
	if err != nil {
		log.Printf("Error getting user tier info after: %v", err)
		return
	}
	
	fmt.Printf("\nUser tier info after:\n")
	fmt.Printf("  Total Points: %d (change: %+d)\n", tierInfoAfter.TotalPoints, tierInfoAfter.TotalPoints - tierInfo.TotalPoints)
	fmt.Printf("  Current Tier: %d\n", tierInfoAfter.CurrentTier)
	
	// Get task progress after
	progressAfter, err := service.GetTaskProgress(ctx, userID, memeTradeTask.ID)
	if err != nil {
		log.Printf("Error getting task progress after: %v", err)
		return
	}
	
	fmt.Printf("\nTask Progress after:\n")
	fmt.Printf("  Status: %s\n", progressAfter.Status)
	fmt.Printf("  Completion Count: %d (change: %+d)\n", progressAfter.CompletionCount, progressAfter.CompletionCount - progress.CompletionCount)
	fmt.Printf("  Last Completed At: %v\n", progressAfter.LastCompletedAt)
	fmt.Printf("  Points Earned: %d (change: %+d)\n", progressAfter.PointsEarned, progressAfter.PointsEarned - progress.PointsEarned)
}
