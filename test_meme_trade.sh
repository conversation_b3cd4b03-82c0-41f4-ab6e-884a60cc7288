#!/bin/bash

# Test MEME trade completion for user 019629ba-7877-7a9c-bac0-0ee50d7e8bd5

USER_ID="019629ba-7877-7a9c-bac0-0ee50d7e8bd5"
ORDER_ID=$(uuidgen)

echo "Testing MEME trade completion for user: $USER_ID"
echo "Order ID: $ORDER_ID"

# Create test NATS message
cat > test_trade_message.json << EOF
{
  "user_id": "$USER_ID",
  "order_id": "$ORDER_ID",
  "quote_amount": 0.00519948,
  "quote_symbol": "SOL",
  "trade_data": {
    "base_symbol": "TRUMP",
    "order_id": "$ORDER_ID",
    "quote_amount": 0.00519948,
    "quote_symbol": "SOL",
    "trade_type": "MEME",
    "tx_hash": "test_tx_hash_$(date +%s)",
    "user_id": "$USER_ID",
    "volume": 0.00519948
  }
}
EOF

echo "Test message created:"
cat test_trade_message.json

echo ""
echo "You can now publish this message to NATS to test the trade completion."
echo "Watch the logs for detailed debugging information."
