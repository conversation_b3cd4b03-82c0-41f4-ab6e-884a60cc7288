package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

type DebugController struct{}

// DebugUserTaskProgress debugs user task progress
func (d *DebugController) DebugUserTaskProgress(c *gin.Context) {
	userIDStr := c.Param("userId")
	taskIdentifier := c.Param("taskIdentifier")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	debugService := activity_cashback.NewDebugService()
	err = debugService.GetUserTaskProgressInfo(c.Request.Context(), userID, model.TaskIdentifier(taskIdentifier))
	if err != nil {
		global.GVA_LOG.Error("Failed to get user task progress info", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Debug info logged, check server logs"})
}

// ResetUserTaskProgress resets user task progress
func (d *DebugController) ResetUserTaskProgress(c *gin.Context) {
	userIDStr := c.Param("userId")
	taskIDStr := c.Param("taskId")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	taskID, err := uuid.Parse(taskIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid task ID"})
		return
	}

	debugService := activity_cashback.NewDebugService()
	err = debugService.ResetUserTaskProgress(c.Request.Context(), userID, taskID)
	if err != nil {
		global.GVA_LOG.Error("Failed to reset user task progress", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Task progress reset successfully"})
}

// TestTaskCompletion tests task completion
func (d *DebugController) TestTaskCompletion(c *gin.Context) {
	userIDStr := c.Param("userId")
	taskIdentifier := c.Param("taskIdentifier")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	debugService := activity_cashback.NewDebugService()
	err = debugService.TestTaskCompletion(c.Request.Context(), userID, model.TaskIdentifier(taskIdentifier))
	if err != nil {
		global.GVA_LOG.Error("Failed to test task completion", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Task completion test completed, check server logs"})
}
