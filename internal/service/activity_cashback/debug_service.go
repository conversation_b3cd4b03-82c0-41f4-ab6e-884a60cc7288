package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// DebugService provides debugging utilities for activity cashback system
type DebugService struct {
	service ActivityCashbackServiceInterface
}

// NewDebugService creates a new debug service
func NewDebugService() *DebugService {
	return &DebugService{
		service: NewActivityCashbackService(),
	}
}

// ResetUserTaskProgress resets task progress for a user for a specific task
func (d *DebugService) ResetUserTaskProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	global.GVA_LOG.Info("Resetting task progress for user",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))

	// Get current progress
	progress, err := d.service.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Reset progress
	progress.Status = model.TaskStatusNotStarted
	progress.CompletionCount = 0
	progress.LastCompletedAt = nil
	progress.PointsEarned = 0
	progress.UpdatedAt = time.Now()

	// Update progress
	if err := d.service.UpdateTaskProgress(ctx, progress); err != nil {
		return fmt.Errorf("failed to update task progress: %w", err)
	}

	global.GVA_LOG.Info("Task progress reset successfully",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))

	return nil
}

// GetUserTaskProgressInfo gets detailed task progress info for debugging
func (d *DebugService) GetUserTaskProgressInfo(ctx context.Context, userID uuid.UUID, taskIdentifier model.TaskIdentifier) error {
	global.GVA_LOG.Info("Getting task progress info for debugging",
		zap.String("user_id", userID.String()),
		zap.String("task_identifier", string(taskIdentifier)))

	// Get tasks by identifier
	tasks, err := d.service.GetTasksByCategory(ctx, model.CategoryTrading)
	if err != nil {
		return fmt.Errorf("failed to get tasks: %w", err)
	}

	var matchingTasks []model.ActivityTask
	for _, task := range tasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == taskIdentifier {
			matchingTasks = append(matchingTasks, task)
		}
	}

	global.GVA_LOG.Info("Found matching tasks",
		zap.String("task_identifier", string(taskIdentifier)),
		zap.Int("count", len(matchingTasks)))

	for _, task := range matchingTasks {
		global.GVA_LOG.Info("Task details",
			zap.String("task_id", task.ID.String()),
			zap.String("task_name", task.Name),
			zap.String("category", string(task.Category.Name)),
			zap.String("frequency", string(task.Frequency)),
			zap.Int("points", task.Points),
			zap.Bool("is_active", task.IsActive))

		// Get progress for this task
		progress, err := d.service.GetTaskProgress(ctx, userID, task.ID)
		if err != nil {
			global.GVA_LOG.Error("Failed to get task progress",
				zap.Error(err),
				zap.String("task_id", task.ID.String()))
			continue
		}

		global.GVA_LOG.Info("Task progress details",
			zap.String("task_id", task.ID.String()),
			zap.String("status", string(progress.Status)),
			zap.Int("completion_count", progress.CompletionCount),
			zap.Any("last_completed_at", progress.LastCompletedAt),
			zap.Int("points_earned", progress.PointsEarned))
	}

	return nil
}

// TestTaskCompletion tests task completion for debugging
func (d *DebugService) TestTaskCompletion(ctx context.Context, userID uuid.UUID, taskIdentifier model.TaskIdentifier) error {
	global.GVA_LOG.Info("Testing task completion",
		zap.String("user_id", userID.String()),
		zap.String("task_identifier", string(taskIdentifier)))

	// Get user tier info before
	tierInfoBefore, err := d.service.GetUserTierInfo(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user tier info before: %w", err)
	}

	global.GVA_LOG.Info("User tier info before task completion",
		zap.String("user_id", userID.String()),
		zap.Int("total_points", tierInfoBefore.TotalPoints))

	// Create test trade data
	tradeData := map[string]interface{}{
		"volume":     0.00519948,
		"trade_type": "MEME",
		"order_id":   uuid.New().String(),
		"user_id":    userID.String(),
	}

	// Process task
	registry := NewTaskRegistry(d.service)
	err = registry.ProcessTaskByIdentifier(ctx, userID, taskIdentifier, model.CategoryTrading, tradeData)
	if err != nil {
		global.GVA_LOG.Error("Failed to process task", zap.Error(err))
		return fmt.Errorf("failed to process task: %w", err)
	}

	// Get user tier info after
	tierInfoAfter, err := d.service.GetUserTierInfo(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user tier info after: %w", err)
	}

	pointsChange := tierInfoAfter.TotalPoints - tierInfoBefore.TotalPoints
	global.GVA_LOG.Info("User tier info after task completion",
		zap.String("user_id", userID.String()),
		zap.Int("total_points", tierInfoAfter.TotalPoints),
		zap.Int("points_change", pointsChange))

	if pointsChange == 0 {
		global.GVA_LOG.Warn("No points were added - task may have been already completed or failed")
	} else {
		global.GVA_LOG.Info("Points added successfully", zap.Int("points_added", pointsChange))
	}

	return nil
}
