package main

import (
	"context"
	"fmt"
	"log"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

func main() {
	// Initialize logger
	logger, _ := zap.NewDevelopment()
	global.GVA_LOG = logger

	// Test user ID from the log
	userID, _ := uuid.Parse("019629ba-7877-7a9c-bac0-0ee50d7e8bd5")
	
	// Initialize services
	service := activity_cashback.NewActivityCashbackService()
	
	ctx := context.Background()
	
	// Get user tier info
	tierInfo, err := service.GetUserTierInfo(ctx, userID)
	if err != nil {
		log.Printf("Error getting user tier info: %v", err)
		return
	}
	
	fmt.Printf("User Tier Info:\n")
	fmt.Printf("  User ID: %s\n", tierInfo.UserID)
	fmt.Printf("  Total Points: %d\n", tierInfo.TotalPoints)
	fmt.Printf("  Points This Month: %d\n", tierInfo.PointsThisMonth)
	fmt.Printf("  Current Tier: %d\n", tierInfo.CurrentTier)
	fmt.Printf("  Trading Volume USD: %s\n", tierInfo.TradingVolumeUSD.String())
	fmt.Printf("  Active Days This Month: %d\n", tierInfo.ActiveDaysThisMonth)
	fmt.Printf("  Cumulative Cashback USD: %s\n", tierInfo.CumulativeCashbackUSD.String())
	fmt.Printf("  Claimable Cashback USD: %s\n", tierInfo.ClaimableCashbackUSD.String())
	fmt.Printf("  Claimed Cashback USD: %s\n", tierInfo.ClaimedCashbackUSD.String())
	fmt.Printf("  Last Activity Date: %v\n", tierInfo.LastActivityDate)
	fmt.Printf("  Tier Upgraded At: %v\n", tierInfo.TierUpgradedAt)
	fmt.Printf("  Monthly Reset At: %v\n", tierInfo.MonthlyResetAt)
	fmt.Printf("  Created At: %v\n", tierInfo.CreatedAt)
	fmt.Printf("  Updated At: %v\n", tierInfo.UpdatedAt)
	
	// Try to add 200 points manually
	fmt.Printf("\nTrying to add 200 points manually...\n")
	err = service.AddPoints(ctx, userID, 200, "manual_test")
	if err != nil {
		log.Printf("Error adding points: %v", err)
	} else {
		fmt.Printf("Points added successfully!\n")
		
		// Get updated tier info
		updatedTierInfo, err := service.GetUserTierInfo(ctx, userID)
		if err != nil {
			log.Printf("Error getting updated tier info: %v", err)
		} else {
			fmt.Printf("Updated Total Points: %d (change: %+d)\n", 
				updatedTierInfo.TotalPoints, 
				updatedTierInfo.TotalPoints - tierInfo.TotalPoints)
		}
	}
}
